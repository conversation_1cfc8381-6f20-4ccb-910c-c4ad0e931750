<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 操作成功</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: transparent;
            border: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #171A1F;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #171A1F;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #171A1F;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #171A1F;
            border-radius: 1px;
        }

        .success-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-top: -100px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 16px;
        }

        .success-icon img {
            width: 100%;
            height: 100%;
        }

        .success-title {
            font-family: 'Archivo', sans-serif;
            font-weight: 700;
            font-size: 22px;
            line-height: 34px;
            color: #171A1F;
            margin-bottom: 40px;
        }

        .return-button {
            position: absolute;
            bottom: 46px;
            left: 20px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: 1px solid transparent;
            border-radius: 16px;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .return-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(99, 106, 232, 0.2);
        }

        .return-button:active {
            transform: scale(0.98);
        }

        /* 页面加载动画 */
        .success-container {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .success-icon {
            animation: scaleIn 0.8s ease-out 0.2s forwards;
            transform: scale(0);
        }

        .success-title {
            animation: fadeIn 0.6s ease-out 0.4s forwards;
            opacity: 0;
        }

        .return-button {
            animation: fadeIn 0.6s ease-out 0.6s forwards;
            opacity: 0;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            to {
                transform: scale(1);
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 成功内容 -->
    <div class="success-container">
        <div class="success-icon">
            <img src="assets/success-icon.svg" alt="成功图标">
        </div>
        <div class="success-title">成功</div>
    </div>

    <!-- 返回按钮 -->
    <button class="return-button" onclick="goBack()">返回</button>

    <script>
        // 返回功能
        function goBack() {
            // 根据来源页面决定返回位置
            const referrer = document.referrer;
            
            if (referrer.includes('role-selection.html')) {
                // 从身份选择页面来的，返回到登录页面
                window.location.href = 'index.html';
            } else {
                // 其他情况，返回上一页
                window.history.back();
            }
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加其他初始化逻辑
            console.log('操作成功页面加载完成');
        });
    </script>
</body>
</html>
