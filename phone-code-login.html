<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 手机验证码登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: transparent;
            border: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #171A1F;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #171A1F;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #171A1F;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #171A1F;
            border-radius: 1px;
        }

        .back-button {
            position: absolute;
            top: 52px;
            left: 33px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button svg {
            width: 18px;
            height: 12px;
            stroke: #171A1F;
            stroke-width: 2px;
        }

        .title {
            position: absolute;
            top: 122px;
            left: 33px;
            font-family: 'Archivo', sans-serif;
            font-weight: 700;
            font-size: 32px;
            line-height: 48px;
            color: #171A1F;
        }

        .subtitle {
            position: absolute;
            top: 170px;
            left: 33px;
            font-family: 'Archivo', sans-serif;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            color: #9095A0;
        }

        .form-group {
            position: absolute;
            left: 33px;
            width: 324px;
        }

        .phone-group {
            top: 224px;
            height: 71px;
        }

        .code-group {
            top: 311px;
            height: 71px;
        }

        .form-label {
            font-weight: 700;
            font-size: 16px;
            line-height: 26px;
            color: #424955;
            margin-bottom: 4px;
        }

        .form-input {
            width: 323px;
            height: 43px;
            background: #F3F4F6;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 9px 16px;
            font-size: 16px;
            line-height: 26px;
            color: #171A1F;
            outline: none;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            border-color: #636AE8;
            background: #FFFFFF;
            box-shadow: 0 0 0 3px rgba(99, 106, 232, 0.1);
        }

        .form-input::placeholder {
            color: #BCC1CA;
        }

        .code-input-container {
            position: relative;
        }

        .send-code-btn {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #636AE8;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            padding: 4px 8px;
        }

        .send-code-btn:hover {
            text-decoration: underline;
        }

        .send-code-btn:disabled {
            color: #BCC1CA;
            cursor: not-allowed;
        }

        .divider {
            position: absolute;
            top: 350px;
            left: 241px;
            width: 1px;
            height: 18px;
            background: #BCC1CA;
        }

        .login-button {
            position: absolute;
            top: 746px;
            left: 20px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: 1px solid transparent;
            border-radius: 16px;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(99, 106, 232, 0.2);
        }

        .login-button:disabled {
            background: #BCC1CA;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>

    <!-- 标题 -->
    <div class="title">Hello!</div>
    <div class="subtitle">请使用手机验证码登录</div>

    <!-- 手机号输入 -->
    <div class="form-group phone-group">
        <div class="form-label">手机号码</div>
        <input type="tel" class="form-input" id="phoneInput" placeholder="请输入手机号码" maxlength="11">
    </div>

    <!-- 验证码输入 -->
    <div class="form-group code-group">
        <div class="form-label">密码</div>
        <div class="code-input-container">
            <input type="text" class="form-input" id="codeInput" placeholder="请输入验证码" maxlength="6">
            <button class="send-code-btn" id="sendCodeBtn" onclick="sendCode()">发送验证码</button>
        </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 登录按钮 -->
    <button class="login-button" id="loginBtn" onclick="login()">登录</button>

    <script>
        let countdown = 0;
        let countdownTimer = null;

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 发送验证码
        function sendCode() {
            const phoneInput = document.getElementById('phoneInput');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            
            const phone = phoneInput.value.trim();
            
            // 验证手机号
            if (!phone) {
                alert('请输入手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            // 开始倒计时
            countdown = 60;
            sendCodeBtn.disabled = true;
            updateCountdown();
            
            countdownTimer = setInterval(() => {
                countdown--;
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                } else {
                    updateCountdown();
                }
            }, 1000);
            
            // 模拟发送验证码
            alert(`验证码已发送至 ${phone}`);
        }

        // 更新倒计时显示
        function updateCountdown() {
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            sendCodeBtn.textContent = `${countdown}s后重发`;
        }

        // 登录
        function login() {
            const phoneInput = document.getElementById('phoneInput');
            const codeInput = document.getElementById('codeInput');
            
            const phone = phoneInput.value.trim();
            const code = codeInput.value.trim();
            
            // 验证输入
            if (!phone) {
                alert('请输入手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!code) {
                alert('请输入验证码');
                codeInput.focus();
                return;
            }
            
            if (code.length !== 6) {
                alert('请输入6位验证码');
                codeInput.focus();
                return;
            }
            
            // 模拟登录
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            setTimeout(() => {
                alert('登录成功！');
                // 跳转到身份选择页面
                window.location.href = 'role-selection.html';
            }, 1500);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 预填充手机号（模拟从上一页传递）
            document.getElementById('phoneInput').value = '13860888888';
            
            // 页面加载动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
