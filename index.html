<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 登录页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: #FFFFFF;
            border: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #171A1F;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #171A1F;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #171A1F;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #171A1F;
            border-radius: 1px;
        }

        .logo-container {
            position: absolute;
            top: 303px;
            left: 95.5px;
            width: 199px;
            height: 40px;
        }

        .logo {
            width: 100%;
            height: 100%;
        }

        .welcome-text {
            position: absolute;
            top: 367px;
            left: 40.5px;
            width: 308px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            color: #9095A0;
        }

        .login-button {
            position: absolute;
            width: 350px;
            height: 52px;
            left: 20px;
            border-radius: 26px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 400;
            color: #FFFFFF;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .wechat-login {
            top: 587px;
            background: #379AE6;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .phone-code-login {
            top: 651px;
            background: #636AE8;
            box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
        }

        .phone-password-login {
            top: 715px;
            background: #7F55E0;
            box-shadow: 0px 4px 9px 0px rgba(127, 85, 224, 0.11), 0px 0px 2px 0px rgba(127, 85, 224, 0.12);
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.15);
        }

        .button-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .button-icon svg {
            width: 100%;
            height: 100%;
        }

        .bottom-indicator {
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            width: 160px;
            height: 5px;
            background: #171A1F;
            border: 1px solid #BCC1CA;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- Logo -->
    <div class="logo-container">
        <img src="assets/logo.svg" alt="慧习作 Logo" class="logo">
    </div>

    <!-- 欢迎文字 -->
    <div class="welcome-text">欢迎使用慧习作</div>

    <!-- 登录按钮 -->
    <button class="login-button wechat-login">
        <div class="button-icon">
            <img src="assets/wechat-icon.svg" alt="微信图标">
        </div>
        <span>授权登录</span>
    </button>

    <button class="login-button phone-code-login">
        <div class="button-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z" stroke="white" stroke-width="2" fill="none"/>
                <path d="M8 6h8M8 10h8M8 14h4" stroke="white" stroke-width="2"/>
                <circle cx="12" cy="18" r="1" fill="white"/>
            </svg>
        </div>
        <span>手机验证码</span>
    </button>

    <button class="login-button phone-password-login">
        <div class="button-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="11" width="18" height="10" rx="2" ry="2" stroke="white" stroke-width="2" fill="none"/>
                <circle cx="12" cy="16" r="1" fill="white"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="white" stroke-width="2" fill="none"/>
            </svg>
        </div>
        <span>手机密码</span>
    </button>

    <!-- 底部指示器 -->
    <div class="bottom-indicator"></div>

    <script>
        // 登录按钮点击事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const wechatBtn = document.querySelector('.wechat-login');
            const phoneCodeBtn = document.querySelector('.phone-code-login');
            const phonePasswordBtn = document.querySelector('.phone-password-login');

            // 微信授权登录
            wechatBtn.addEventListener('click', function() {
                // 添加点击动画效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // 模拟微信授权流程
                showLoading('正在跳转微信授权...');
                setTimeout(() => {
                    hideLoading();
                    alert('微信授权登录功能需要在微信环境中使用');
                }, 1500);
            });

            // 手机验证码登录
            phoneCodeBtn.addEventListener('click', function() {
                // 添加点击动画效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // 跳转到验证码登录页面
                showLoading('正在跳转...');
                setTimeout(() => {
                    window.location.href = 'phone-code-login.html';
                }, 800);
            });

            // 手机密码登录
            phonePasswordBtn.addEventListener('click', function() {
                // 添加点击动画效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // 跳转到密码登录页面
                showLoading('正在跳转...');
                setTimeout(() => {
                    window.location.href = 'phone-password-login.html';
                }, 800);
            });
        });

        // 显示加载状态
        function showLoading(message = '加载中...') {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">${message}</div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }

        // 隐藏加载状态
        function hideLoading() {
            const loadingDiv = document.getElementById('loading-overlay');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        // 页面加载动画
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>

    <style>
        /* 加载遮罩层样式 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-in-out;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #636AE8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        .loading-text {
            color: #171A1F;
            font-size: 14px;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 按钮点击效果优化 */
        .login-button {
            transition: all 0.2s ease;
        }

        .login-button:active {
            transform: scale(0.98);
        }
    </style>
</body>
</html>
