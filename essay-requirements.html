<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 作文要求</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 375px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
            overflow-y: auto;
        }

        /* 状态栏 */
        .status-bar {
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 40px;
            background: rgba(255, 255, 255, 0);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            z-index: 1000;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #000000;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #000000;
            border-radius: 1px;
        }

        /* 主要内容区域 */
        .main-content {
            padding-top: 40px;
            min-height: 100vh;
            padding-bottom: 20px;
        }

        /* 头部区域 */
        .header {
            display: flex;
            align-items: center;
            padding: 8px 24px;
            position: relative;
        }

        .back-button {
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 16px;
            line-height: 26px;
            color: #323842;
        }

        /* 作文信息区域 */
        .essay-info-section {
            background: #F8F9FA;
            padding: 8px 26px;
        }

        .essay-info-row {
            display: flex;
            gap: 12px;
            margin-bottom: 0;
        }

        .essay-info-item {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
        }

        /* 详细信息区域 */
        .details-section {
            padding: 18px 26px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 8px;
        }

        .detail-label {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            width: 56px;
            flex-shrink: 0;
        }

        .detail-value {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            flex: 1;
        }

        .detail-requirements {
            margin-top: 12px;
        }

        .requirements-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            margin-top: 8px;
        }

        /* 分隔线 */
        .divider {
            height: 20px;
            background: #F8F9FA;
        }

        /* 上传区域 */
        .upload-section {
            padding: 12px 24px;
        }

        .upload-title {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .warning-icon {
            width: 16px;
            height: 16px;
        }

        .upload-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .upload-item {
            position: relative;
        }

        .upload-box {
            width: 131px;
            height: 76px;
            border: none;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-box svg {
            width: 131px;
            height: 76px;
            object-fit: cover;
        }

        .upload-box.dashed {
            border: 1px dashed #DEE1E6;
            background: #FFFFFF;
        }

        .upload-plus {
            width: 18px;
            height: 18px;
            color: #BCC1CA;
        }

        .delete-button {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 16px;
            height: 16px;
            background: transparent;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
        }

        .delete-icon {
            width: 12px;
            height: 12px;
            color: #DE3B40;
        }

        /* 提交按钮 */
        .submit-button {
            margin: 20px 12px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: none;
            border-radius: 16px;
            color: #FFFFFF;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 28px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .submit-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(99, 106, 232, 0.2);
        }

        .submit-button:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 头部区域 -->
        <div class="header">
            <div class="back-button" onclick="goBack()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20.5999 12L3.3999 12" stroke="#171A1F" stroke-width="2.064" stroke-miterlimit="10"/>
                    <path d="M9.43992 18.02L3.41992 12L9.43992 5.97998" stroke="#171A1F" stroke-width="2.064" stroke-miterlimit="10" stroke-linecap="square"/>
                </svg>
            </div>
            <div class="page-title">作文要求</div>
        </div>

        <!-- 作文信息区域 -->
        <div class="essay-info-section">
            <div class="essay-info-row">
                <span class="essay-info-item">第三单元</span>
                <span class="essay-info-item">单元作文</span>
                <span class="essay-info-item">全命题</span>
            </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="details-section">
            <div class="detail-row">
                <span class="detail-label">作文命题</span>
                <span class="detail-value">我的植物朋友</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">字数要求</span>
                <span class="detail-value">300字</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">总分</span>
                <span class="detail-value">30分</span>
            </div>
            <div class="detail-requirements">
                <div class="detail-row">
                    <span class="detail-label">作文要求</span>
                    <span class="detail-value">选择一项自己做过的小实验（可以是科学课上的，也可以是自己在家尝试的），按照"实验准备—实验过程—实验结果"的顺序写下来。重点把实验过程写清楚，可以用上"先……接着……然后……最后……"等表示顺序的词语。</span>
                </div>
            </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider"></div>

        <!-- 上传区域 -->
        <div class="upload-section">
            <div class="upload-title">
                <svg class="warning-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.00001 1.76001C4.55375 1.76001 1.76001 4.55375 1.76001 8.00001C1.76001 11.4463 4.55375 14.24 8.00001 14.24C11.4463 14.24 14.24 11.4463 14.24 8.00001C14.2364 4.55526 11.4448 1.76365 8.00001 1.76001ZM7.52001 5.12001C7.52001 4.85491 7.73491 4.64001 8.00001 4.64001C8.26511 4.64001 8.48001 4.85491 8.48001 5.12001L8.48001 8.48001C8.48001 8.74511 8.26511 8.96001 8.00001 8.96001C7.73491 8.96001 7.52001 8.74511 7.52001 8.48001L7.52001 5.12001ZM8.00001 11.36C7.60236 11.36 7.28001 11.0377 7.28001 10.64C7.28001 10.2424 7.60236 9.92001 8.00001 9.92001C8.39765 9.92001 8.72001 10.2424 8.72001 10.64C8.72001 11.0377 8.39765 11.36 8.00001 11.36Z" fill="#9095A0"/>
                </svg>
                上传作文
            </div>
            <div class="upload-grid">
                <!-- 左上：图片框 -->
                <div class="upload-item">
                    <div class="upload-box">
                        <svg width="132" height="78" viewBox="0 0 132 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" fill="white"/>
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M0.744613 1.30552L128.994 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M128.994 1.30552L0.744613 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="delete-button">
                        <svg class="delete-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1_549)">
                                <path d="M7.99992 14.6666C11.6818 14.6666 14.6666 11.6818 14.6666 7.99992C14.6666 4.31802 11.6818 1.33325 7.99992 1.33325C4.31802 1.33325 1.33325 4.31802 1.33325 7.99992C1.33325 11.6818 4.31802 14.6666 7.99992 14.6666Z" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M5.33325 8H10.6666" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_1_549">
                                    <rect width="16" height="16" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                </div>
                <!-- 右上：图片框 -->
                <div class="upload-item">
                    <div class="upload-box">
                        <svg width="132" height="78" viewBox="0 0 132 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" fill="white"/>
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M0.744613 1.30552L128.994 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M128.994 1.30552L0.744613 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="delete-button">
                        <svg class="delete-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1_549_2)">
                                <path d="M7.99992 14.6666C11.6818 14.6666 14.6666 11.6818 14.6666 7.99992C14.6666 4.31802 11.6818 1.33325 7.99992 1.33325C4.31802 1.33325 1.33325 4.31802 1.33325 7.99992C1.33325 11.6818 4.31802 14.6666 7.99992 14.6666Z" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M5.33325 8H10.6666" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_1_549_2">
                                    <rect width="16" height="16" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                </div>
                <!-- 左下：图片框 -->
                <div class="upload-item">
                    <div class="upload-box">
                        <svg width="132" height="78" viewBox="0 0 132 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" fill="white"/>
                            <rect x="2.24461" y="2.80552" width="128.255" height="73.1482" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M0.744613 1.30552L128.994 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                            <path d="M128.994 1.30552L0.744613 74.4478" stroke="#BCC1CA" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="delete-button">
                        <svg class="delete-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1_549_3)">
                                <path d="M7.99992 14.6666C11.6818 14.6666 14.6666 11.6818 14.6666 7.99992C14.6666 4.31802 11.6818 1.33325 7.99992 1.33325C4.31802 1.33325 1.33325 4.31802 1.33325 7.99992C1.33325 11.6818 4.31802 14.6666 7.99992 14.6666Z" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M5.33325 8H10.6666" stroke="#DE3B40" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_1_549_3">
                                    <rect width="16" height="16" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                </div>
                <!-- 右下：添加按钮 -->
                <div class="upload-item">
                    <div class="upload-box dashed">
                        <svg class="upload-plus" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <button class="submit-button" onclick="submitEssay()">提交</button>
    </div>

    <script>
        // 返回功能
        function goBack() {
            window.location.href = 'dashboard.html';
        }

        // 提交作文
        function submitEssay() {
            // 这里可以添加提交逻辑
            alert('作文提交成功！');
            window.location.href = 'success.html';
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('作文要求页面加载完成');
        });
    </script>
</body>
</html>
