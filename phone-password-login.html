<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 手机密码登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: transparent;
            border: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #171A1F;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #171A1F;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #171A1F;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #171A1F;
            border-radius: 1px;
        }

        .back-button {
            position: absolute;
            top: 52px;
            left: 33px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button svg {
            width: 18px;
            height: 12px;
            stroke: #171A1F;
            stroke-width: 2px;
        }

        .title {
            position: absolute;
            top: 122px;
            left: 33px;
            font-family: 'Archivo', sans-serif;
            font-weight: 700;
            font-size: 32px;
            line-height: 48px;
            color: #171A1F;
        }

        .subtitle {
            position: absolute;
            top: 170px;
            left: 33px;
            font-family: 'Archivo', sans-serif;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            color: #9095A0;
        }

        .form-group {
            position: absolute;
            left: 33px;
            width: 324px;
        }

        .phone-group {
            top: 224px;
            height: 71px;
        }

        .password-group {
            top: 311px;
            height: 71px;
        }

        .form-label {
            font-weight: 700;
            font-size: 16px;
            line-height: 26px;
            color: #424955;
            margin-bottom: 4px;
        }

        .form-input {
            width: 323px;
            height: 43px;
            background: #F3F4F6;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 9px 16px;
            font-size: 16px;
            line-height: 26px;
            color: #171A1F;
            outline: none;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            border-color: #7F55E0;
            background: #FFFFFF;
            box-shadow: 0 0 0 3px rgba(127, 85, 224, 0.1);
        }

        .form-input::placeholder {
            color: #BCC1CA;
        }

        .password-input-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            color: #9095A0;
        }

        .password-toggle:hover {
            color: #7F55E0;
        }

        .forgot-password {
            position: absolute;
            top: 400px;
            right: 33px;
            color: #7F55E0;
            font-size: 14px;
            text-decoration: none;
            cursor: pointer;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-button {
            position: absolute;
            top: 746px;
            left: 20px;
            width: 350px;
            height: 52px;
            background: #7F55E0;
            border: 1px solid transparent;
            border-radius: 16px;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0px 4px 9px 0px rgba(127, 85, 224, 0.11), 0px 0px 2px 0px rgba(127, 85, 224, 0.12);
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(127, 85, 224, 0.2);
        }

        .login-button:disabled {
            background: #BCC1CA;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .register-link {
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            color: #9095A0;
            font-size: 14px;
            text-align: center;
        }

        .register-link a {
            color: #7F55E0;
            text-decoration: none;
        }

        .register-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>

    <!-- 标题 -->
    <div class="title">Hello!</div>
    <div class="subtitle">请使用手机密码登录</div>

    <!-- 手机号输入 -->
    <div class="form-group phone-group">
        <div class="form-label">手机号码</div>
        <input type="tel" class="form-input" id="phoneInput" placeholder="请输入手机号码" maxlength="11">
    </div>

    <!-- 密码输入 -->
    <div class="form-group password-group">
        <div class="form-label">密码</div>
        <div class="password-input-container">
            <input type="password" class="form-input" id="passwordInput" placeholder="请输入密码">
            <button class="password-toggle" onclick="togglePassword()">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" id="eyeIcon">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- 忘记密码链接 -->
    <a href="#" class="forgot-password" onclick="forgotPassword()">忘记密码？</a>

    <!-- 登录按钮 -->
    <button class="login-button" id="loginBtn" onclick="login()">登录</button>

    <!-- 注册链接 -->
    <div class="register-link">
        还没有账号？<a href="#" onclick="register()">立即注册</a>
    </div>

    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 切换密码显示/隐藏
        function togglePassword() {
            const passwordInput = document.getElementById('passwordInput');
            const eyeIcon = document.getElementById('eyeIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2" fill="none"/>
                    <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                `;
            }
        }

        // 忘记密码
        function forgotPassword() {
            alert('忘记密码功能开发中，请联系客服或使用验证码登录');
        }

        // 注册
        function register() {
            alert('注册功能开发中，请联系客服');
        }

        // 登录
        function login() {
            const phoneInput = document.getElementById('phoneInput');
            const passwordInput = document.getElementById('passwordInput');
            
            const phone = phoneInput.value.trim();
            const password = passwordInput.value.trim();
            
            // 验证输入
            if (!phone) {
                alert('请输入手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!password) {
                alert('请输入密码');
                passwordInput.focus();
                return;
            }
            
            if (password.length < 6) {
                alert('密码长度不能少于6位');
                passwordInput.focus();
                return;
            }
            
            // 模拟登录
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            setTimeout(() => {
                alert('登录成功！');
                // 跳转到身份选择页面
                window.location.href = 'role-selection.html';
            }, 1500);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 预填充手机号（模拟从上一页传递）
            document.getElementById('phoneInput').value = '13860888888';
            
            // 页面加载动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
            
            // 回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>
