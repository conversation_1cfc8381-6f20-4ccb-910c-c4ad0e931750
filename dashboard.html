<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 375px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
            overflow-y: auto;
        }

        /* 状态栏 */
        .status-bar {
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 40px;
            background: rgba(255, 255, 255, 0);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            z-index: 1000;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #000000;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #000000;
            border-radius: 1px;
        }

        /* 主要内容区域 */
        .main-content {
            padding-top: 40px;
            min-height: 100vh;
            padding-bottom: 60px;
        }

        /* 用户信息区域 */
        .user-header {
            height: 52px;
            display: flex;
            align-items: center;
            padding: 0 24px;
            background: #FFFFFF;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #CED0F8;
            border: 1px solid #636AE8;
            margin-right: 14px;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-name {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 18px;
            line-height: 28px;
            color: #323842;
        }

        /* 孩子选择区域 */
        .children-section {
            background: #F8F9FA;
            padding: 18px 12px;
        }

        .children-list {
            display: flex;
            align-items: flex-start;
            justify-content: space-around;
            gap: 12px;
        }

        .add-child {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background: #FFFFFF;
            border: 2px dashed #7F55E0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0px 0px 1px 0px rgba(23, 26, 31, 0.07), 0px 0px 2px 0px rgba(23, 26, 31, 0.12);
        }

        .add-child svg {
            width: 24px;
            height: 24px;
            stroke: #7F55E0;
            stroke-width: 2px;
        }

        .child-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .child-avatar {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: 2px solid;
            overflow: hidden;
        }

        .child-avatar.child1 {
            background: #D8CBF5;
            border-color: #7F55E0;
        }

        .child-avatar.child2 {
            background: #BAF3EB;
            border-color: #22CCB2;
        }

        .child-avatar.child3 {
            background: #F8CEDB;
            border-color: #E8618C;
        }

        .child-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .child-name {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #323842;
            text-align: center;
        }

        /* 当前孩子信息 */
        .current-child-info {
            background: #FFFFFF;
            padding: 18px 24px;
        }

        .child-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }

        .current-child-avatar {
            width: 38px;
            height: 38px;
            border-radius: 19px;
            background: #D8CBF5;
            border: 1px solid #7F55E0;
            margin-right: 12px;
            overflow: hidden;
        }

        .current-child-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .child-basic-info {
            flex: 1;
        }

        .child-info-name {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 12px;
            line-height: 20px;
            color: #171A1F;
            margin-bottom: 2px;
        }

        .child-info-birth {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #9095A0;
        }

        .child-info-age {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #9095A0;
            margin-left: 10px;
        }

        .wechat-icon {
            width: 16px;
            height: 16px;
            margin-left: auto;
        }

        .child-school-info {
            display: flex;
            gap: 8px;
            margin-left: 50px;
        }

        .school-info-item {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #171A1F;
        }

        /* 学年信息 */
        .school-year {
            padding: 8px 18px;
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 12px;
            line-height: 20px;
            color: #171A1F;
            background: #F8F9FA;
            border-top: 1px solid #F3F4F6;
            border-bottom: 1px solid #F3F4F6;
        }

        /* 作文列表 */
        .essay-list {
            background: #FFFFFF;
        }

        .essay-item {
            padding: 11px 17px;
            background: #FFFFFF;
            position: relative;
        }

        .essay-header {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }

        .essay-unit, .essay-type, .essay-format {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
        }

        .essay-title {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            margin-bottom: 18px;
        }

        .essay-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .essay-time {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 8px;
            line-height: 14px;
            color: #9095A0;
        }

        .essay-score {
            display: flex;
            align-items: baseline;
            gap: 2px;
        }

        .score-number {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 24px;
            line-height: 36px;
            color: #DE3B40;
        }

        .score-unit {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            color: #DE3B40;
        }

        .upload-button {
            background: #636AE8;
            border: none;
            border-radius: 4px;
            padding: 1px 8px;
            color: #FFFFFF;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 11px;
            line-height: 18px;
            cursor: pointer;
        }

        .arrow-right {
            position: absolute;
            right: 17px;
            top: 12px;
            width: 16px;
            height: 16px;
        }

        /* 分隔线 */
        .divider {
            height: 20px;
            background: #F8F9FA;
        }

        .divider.small {
            height: 4px;
            background: #F8F9FA;
        }

        .bottom-line {
            text-align: center;
            padding: 8px 0;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 8px;
            line-height: 14px;
            color: #9095A0;
            background: #FFFFFF;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 48px;
            background: #FFFFFF;
            display: flex;
            padding: 0 6px;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            cursor: pointer;
        }

        .tab-icon {
            width: 24px;
            height: 24px;
        }

        .tab-label {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 10px;
            line-height: 16px;
            color: #424955;
        }

        .tab-item.active .tab-label {
            font-weight: 700;
            color: #4850E4;
        }


    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 用户信息区域 -->
        <div class="user-header">
            <div class="user-avatar">
                <img src="assets/user-avatar-56586a.png" alt="用户头像">
            </div>
            <div class="user-name">Rachel</div>
        </div>

        <!-- 孩子选择区域 -->
        <div class="children-section">
            <div class="children-list">
                <div class="child-item">
                    <div class="child-avatar child1">
                        <img src="assets/child1-avatar-56586a.png" alt="孩子1头像">
                    </div>
                    <div class="child-name">孩子1</div>
                </div>
                <div class="child-item">
                    <div class="child-avatar child2">
                        <img src="assets/child2-avatar-56586a.png" alt="孩子2头像">
                    </div>
                    <div class="child-name">孩子2</div>
                </div>
                <div class="child-item">
                    <div class="child-avatar child3">
                        <img src="assets/child3-avatar-56586a.png" alt="孩子3头像">
                    </div>
                    <div class="child-name">孩子3</div>
                </div>
                <div class="add-child">
                    <svg viewBox="0 0 24 24" fill="none">
                        <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 当前孩子信息 -->
        <div class="current-child-info">
            <div class="child-info-header">
                <div class="current-child-avatar">
                    <img src="assets/child1-avatar-56586a.png" alt="当前孩子头像">
                </div>
                <div class="child-basic-info">
                    <div style="display: flex; align-items: center;">
                        <span class="child-info-name">孩子1</span>
                        <span class="child-info-birth">2020.06.03</span>
                        <span class="child-info-age">6岁</span>
                    </div>
                </div>
                <div class="wechat-icon">
                    <svg viewBox="0 0 16 16" fill="none">
                        <path d="M8 0C3.6 0 0 3.1 0 7c0 2.2 1.3 4.2 3.3 5.5L2.7 16l3.8-1.9c.5.1 1 .1 1.5.1 4.4 0 8-3.1 8-7S12.4 0 8 0z" fill="#E8618C"/>
                    </svg>
                </div>
            </div>
            <div class="child-school-info">
                <span class="school-info-item">江头中心小学</span>
                <span class="school-info-item">一年一班</span>
                <span class="school-info-item">09号</span>
            </div>
        </div>

        <!-- 学年信息 -->
        <div class="school-year">2025～2026学年 上学期</div>

        <!-- 作文列表 -->
        <div class="essay-list">
            <!-- 第三单元 -->
            <div class="essay-item">
                <div class="essay-header">
                    <span class="essay-unit">第三单元</span>
                    <span class="essay-type">单元作文</span>
                    <span class="essay-format">全命题</span>
                </div>
                <div class="essay-title">我的植物朋友</div>
                <div class="essay-footer">
                    <span class="essay-time">2025.09.30 14:59</span>
                    <button class="upload-button">上传作文</button>
                </div>
                <div class="arrow-right">
                    <svg viewBox="0 0 16 16" fill="none">
                        <path d="M6 4l4 4-4 4" stroke="#323842" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="divider"></div>

            <!-- 第二单元 -->
            <div class="essay-item">
                <div class="essay-header">
                    <span class="essay-unit">第二单元</span>
                    <span class="essay-type">单元作文</span>
                    <span class="essay-format">全命题</span>
                </div>
                <div class="essay-title">我的植物朋友</div>
                <div class="essay-footer">
                    <span class="essay-time">2025.09.08 11:22</span>
                    <div class="essay-score">
                        <span class="score-number">28</span>
                        <span class="score-unit">分</span>
                    </div>
                </div>
                <div class="arrow-right">
                    <svg viewBox="0 0 16 16" fill="none">
                        <path d="M6 4l4 4-4 4" stroke="#323842" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="divider"></div>

            <!-- 第一单元 -->
            <div class="essay-item">
                <div class="essay-header">
                    <span class="essay-unit">第一单元</span>
                    <span class="essay-type">单元作文</span>
                    <span class="essay-format">全命题</span>
                </div>
                <div class="essay-title">我的植物朋友</div>
                <div class="essay-footer">
                    <span class="essay-time">2025.09.08 11:22</span>
                    <div class="essay-score">
                        <span class="score-number">28</span>
                        <span class="score-unit">分</span>
                    </div>
                </div>
                <div class="arrow-right">
                    <svg viewBox="0 0 16 16" fill="none">
                        <path d="M6 4l4 4-4 4" stroke="#323842" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="divider"></div>
        </div>

        <div class="divider small"></div>

        <!-- 底线 -->
        <div class="bottom-line">------我是有底线的------</div>

        <div class="divider small"></div>
    </div>

    <!-- 底部导航 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <div class="tab-icon">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.673266 9.34701L10.4184 1.37369L20.1636 9.34701" stroke="#4850E4" stroke-width="2.064" stroke-miterlimit="10"/>
                    <path d="M8.64657 19.937L8.64657 14.6215H12.1903L12.1903 19.937" stroke="#4850E4" stroke-width="2.064" stroke-miterlimit="10"/>
                    <path d="M3.33112 11.0776L3.33112 18.165C3.33112 19.1439 4.12402 19.9368 5.10297 19.9368L15.7341 19.9368C16.713 19.9368 17.5059 19.1439 17.5059 18.165L17.5059 11.0776" stroke="#4850E4" stroke-width="2.064" stroke-miterlimit="10" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 0L2 0C0.9 0 0 0.9 0 2L0 16C0 17.1 0.9 18 2 18L16 18C17.1 18 18 17.1 18 16L18 2C18 0.9 17.1 0 16 0ZM16 16L2 16L2 2L16 2L16 16ZM4 7L6 7L6 14H4L4 7ZM8 4L10 4L10 14H8L8 4ZM12 10H14L14 14H12L12 10Z" fill="#424955"/>
                </svg>
            </div>
            <div class="tab-label">成长数据</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">
                <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.246 18.246L2.7541 18.246C1.80344 18.246 1.03277 17.4753 1.03277 16.5247L1.03277 1.03277L7.91806 1.03277L10.5 4.47542L19.9673 4.47542L19.9673 16.5247C19.9673 17.4753 19.1966 18.246 18.246 18.246Z" stroke="#424955" stroke-width="2.064" stroke-miterlimit="10" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="tab-label">过往作文</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.8923 17.159C17.5024 14.7562 15.3606 13.0332 12.8611 12.2165C15.4024 10.7036 16.6198 7.67925 15.8352 4.82763C15.0507 1.97601 12.4576 0 9.50001 0C6.54244 0 3.94936 1.97601 3.16481 4.82763C2.38027 7.67925 3.59763 10.7036 6.13897 12.2165C3.63941 13.0323 1.49758 14.7553 0.107718 17.159C-0.0310397 17.3853 -0.0360785 17.669 0.0945585 17.9001C0.225195 18.1311 0.470926 18.273 0.736328 18.2707C1.00173 18.2685 1.24499 18.1223 1.37164 17.8891C3.09095 14.9177 6.12985 13.1437 9.50001 13.1437C12.8702 13.1437 15.9091 14.9177 17.6284 17.8891C17.755 18.1223 17.9983 18.2685 18.2637 18.2707C18.5291 18.273 18.7748 18.1311 18.9055 17.9001C19.0361 17.669 19.0311 17.3853 18.8923 17.159ZM4.38955 6.57308C4.38955 3.75065 6.67759 1.46261 9.50001 1.46261C12.3224 1.46261 14.6105 3.75065 14.6105 6.57308C14.6105 9.39551 12.3224 11.6835 9.50001 11.6835C6.67884 11.6805 4.39257 9.39425 4.38955 6.57308Z" fill="#424955"/>
                </svg>
            </div>
            <div class="tab-label">个人中心</div>
        </div>
    </div>

    <script>
        // 页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 孩子选择功能
            const childItems = document.querySelectorAll('.child-item');
            childItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 这里可以添加切换孩子的逻辑
                    console.log('切换孩子:', this.querySelector('.child-name').textContent);
                });
            });

            // 作文项点击功能
            const essayItems = document.querySelectorAll('.essay-item');
            essayItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 跳转到作文要求页面
                    window.location.href = 'essay-requirements.html';
                });
            });

            // 上传作文按钮
            const uploadButtons = document.querySelectorAll('.upload-button');
            uploadButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    console.log('上传作文');
                });
            });

            // 底部导航切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active状态
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    // 添加当前active状态
                    this.classList.add('active');
                    
                    const tabLabel = this.querySelector('.tab-label').textContent;
                    console.log('切换到:', tabLabel);
                });
            });

            console.log('慧习作首页加载完成');
        });
    </script>
</body>
</html>
